import React, { useContext } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    SafeAreaView,
    ActivityIndicator,
    Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchContractorProfileById } from '../../api/contractor/contractorApi';
import BackButton from '../Components/Shared/BackButton';
import PortfolioGrid from '../Components/Profile/PortfolioGrid';
import { showToast } from '../../utils/showToast';

const { width, height } = Dimensions.get('window');

export default function ContractorProfile() {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const { contractorId } = useLocalSearchParams();

    const {
        data: contractor,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['contractorProfile', contractorId],
        queryFn: () => fetchContractorProfileById(contractorId),
        enabled: !!contractorId,
    });

    const handleHire = () => {
        showToast(
            'info',
            'Coming Soon',
            'Hire functionality will be available soon!'
        );
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <Ionicons key={i} name="star" size={16} color="#FFD700" />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <Ionicons
                    key="half"
                    name="star-half"
                    size={16}
                    color="#FFD700"
                />
            );
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <Ionicons
                    key={`empty-${i}`}
                    name="star-outline"
                    size={16}
                    color="#FFD700"
                />
            );
        }

        return stars;
    };

    if (isLoading) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <BackButton />
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Loading profile...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (isError) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <BackButton />
                <View style={styles.errorContainer}>
                    <Ionicons
                        name="alert-circle"
                        size={48}
                        color={theme.ERROR}
                    />
                    <Text
                        style={[
                            styles.errorText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Failed to load contractor profile
                    </Text>
                    <Text
                        style={[
                            styles.errorSubtext,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {error?.message || 'Please try again later'}
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <BackButton />
            <ScrollView showsVerticalScrollIndicator={false}>
                {/* Header Section */}
                <View style={styles.header}>
                    {/* Avatar */}
                    <View style={styles.avatarContainer}>
                        {contractor?.image ? (
                            <Image
                                source={{ uri: contractor.image }}
                                style={[
                                    styles.avatar,
                                    { borderColor: theme.PRIMARY },
                                ]}
                                resizeMode="cover"
                            />
                        ) : (
                            <View
                                style={[
                                    styles.avatarPlaceholder,
                                    {
                                        backgroundColor: theme.INPUT_BACKGROUND,
                                        borderColor: theme.PRIMARY,
                                    },
                                ]}
                            >
                                <Ionicons
                                    name="person"
                                    size={60}
                                    color={theme.TEXT_SECONDARY}
                                />
                            </View>
                        )}

                        {/* Availability Indicator */}
                        <View
                            style={[
                                styles.availabilityIndicator,
                                {
                                    backgroundColor: contractor?.isAvailable
                                        ? '#4CAF50'
                                        : '#F44336',
                                },
                            ]}
                        >
                            <View style={styles.availabilityDot} />
                        </View>
                    </View>

                    {/* Name and Basic Info */}
                    <Text style={[styles.name, { color: theme.TEXT_PRIMARY }]}>
                        {contractor?.name}
                    </Text>

                    <Text
                        style={[
                            styles.availability,
                            {
                                color: contractor?.isAvailable
                                    ? '#4CAF50'
                                    : '#F44336',
                            },
                        ]}
                    >
                        {contractor?.isAvailable
                            ? 'Available'
                            : 'Not Available'}
                    </Text>
                </View>

                {/* Stats Section */}
                <View
                    style={[
                        styles.statsContainer,
                        { backgroundColor: theme.CARD },
                    ]}
                >
                    <View style={styles.statItem}>
                        <Text
                            style={[
                                styles.statNumber,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            {contractor?.experience || 0}
                        </Text>
                        <Text
                            style={[
                                styles.statLabel,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Years Experience
                        </Text>
                    </View>

                    <View
                        style={[
                            styles.statDivider,
                            { backgroundColor: theme.BORDER },
                        ]}
                    />

                    <View style={styles.statItem}>
                        <View style={styles.ratingContainer}>
                            <View style={styles.starsContainer}>
                                {renderStars(contractor?.ratings || 0)}
                            </View>
                            <Text
                                style={[
                                    styles.ratingText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                {(contractor?.ratings || 0).toFixed(1)}
                            </Text>
                        </View>
                        <Text
                            style={[
                                styles.statLabel,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Rating
                        </Text>
                    </View>

                    <View
                        style={[
                            styles.statDivider,
                            { backgroundColor: theme.BORDER },
                        ]}
                    />

                    <View style={styles.statItem}>
                        <Text
                            style={[
                                styles.statNumber,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            {contractor?.portfolio?.length || 0}
                        </Text>
                        <Text
                            style={[
                                styles.statLabel,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Projects
                        </Text>
                    </View>
                </View>

                {/* Service Areas Section */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Service Areas
                    </Text>
                    <View style={styles.serviceAreasContainer}>
                        {contractor?.serviceAreas &&
                        contractor.serviceAreas.length > 0 ? (
                            contractor.serviceAreas.map((area, index) => (
                                <View
                                    key={index}
                                    style={[
                                        styles.serviceAreaTag,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                            borderColor: theme.PRIMARY,
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.serviceAreaText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        {area}
                                    </Text>
                                </View>
                            ))
                        ) : (
                            <Text
                                style={[
                                    styles.noDataText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                No service areas specified
                            </Text>
                        )}
                    </View>
                </View>

                {/* Specialties Section */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Specialties
                    </Text>
                    <View style={styles.serviceAreasContainer}>
                        {contractor?.specialties &&
                        contractor.specialties.length > 0 ? (
                            contractor.specialties.map((specialty, index) => (
                                <View
                                    key={index}
                                    style={[
                                        styles.specialtyTag,
                                        {
                                            backgroundColor:
                                                theme.SECONDARY + '20',
                                            borderColor: theme.SECONDARY,
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.specialtyText,
                                            { color: theme.SECONDARY },
                                        ]}
                                    >
                                        {specialty}
                                    </Text>
                                </View>
                            ))
                        ) : (
                            <Text
                                style={[
                                    styles.noDataText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                No specialties specified
                            </Text>
                        )}
                    </View>
                </View>

                {/* Portfolio Section */}
                <PortfolioGrid portfolio={contractor?.portfolio} />

                {/* Hire Button */}
                <TouchableOpacity
                    style={[
                        styles.hireButton,
                        { backgroundColor: theme.PRIMARY },
                    ]}
                    onPress={handleHire}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.PRIMARY + 'DD']}
                        style={styles.hireButtonGradient}
                    >
                        <Ionicons name="briefcase" size={20} color="white" />
                        <Text style={styles.hireButtonText}>HIRE</Text>
                    </LinearGradient>
                </TouchableOpacity>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
    },
    errorText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    errorSubtext: {
        fontSize: 14,
        marginTop: 8,
        textAlign: 'center',
    },
    header: {
        alignItems: 'center',
        paddingVertical: 32,
        paddingHorizontal: 24,
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 16,
    },
    avatar: {
        width: 120,
        height: 120,
        borderRadius: 60,
        borderWidth: 4,
    },
    avatarPlaceholder: {
        width: 120,
        height: 120,
        borderRadius: 60,
        borderWidth: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
    availabilityIndicator: {
        position: 'absolute',
        bottom: 8,
        right: 8,
        width: 24,
        height: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 3,
        borderColor: 'white',
    },
    availabilityDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: 'white',
    },
    name: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 4,
        textAlign: 'center',
    },
    availability: {
        fontSize: 16,
        fontWeight: '600',
    },
    statsContainer: {
        flexDirection: 'row',
        marginHorizontal: 16,
        marginBottom: 16,
        borderRadius: 12,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    statItem: {
        flex: 1,
        alignItems: 'center',
    },
    statDivider: {
        width: 1,
        height: '100%',
        marginHorizontal: 12,
    },
    statNumber: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 11,
        textAlign: 'center',
    },
    ratingContainer: {
        alignItems: 'center',
        marginBottom: 4,
    },
    starsContainer: {
        flexDirection: 'row',
        marginBottom: 4,
    },
    ratingText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    section: {
        marginHorizontal: 16,
        marginBottom: 16,
        borderRadius: 12,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    serviceAreasContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    serviceAreaTag: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
    },
    serviceAreaText: {
        fontSize: 14,
        fontWeight: '500',
    },
    specialtyTag: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
    },
    specialtyText: {
        fontSize: 14,
        fontWeight: '500',
    },
    noDataText: {
        fontSize: 14,
        fontStyle: 'italic',
    },
    hireButton: {
        marginHorizontal: 16,
        marginBottom: 32,
        borderRadius: 12,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    hireButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        paddingHorizontal: 24,
    },
    hireButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
});
