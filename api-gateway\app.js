import express from 'express';
import cors from 'cors';
import { createProxyMiddleware } from 'http-proxy-middleware';

const app = express();
const PORT = 8080;

app.use(cors());

function registerProxy(path, target) {
    app.use(
        path,
        createProxyMiddleware({
            target,
            changeOrigin: true,
            logLevel: 'debug',
        })
    );
}

registerProxy('/user-service', 'http://localhost:3007/user-service');
registerProxy('/payment-service', 'http://localhost:3004/payment-service');
registerProxy('/admin-service', 'http://localhost:3001/admin-service');
registerProxy('/customer-service', 'http://localhost:3002/customer-service');
registerProxy(
    '/notification-service',
    'http://localhost:3003/notification-service'
);
registerProxy('/rating-service', 'http://localhost:3005/rating-service');
registerProxy('/site-service', 'http://localhost:3006/site-service');

app.use((req, res) => {
    res.status(502).json({ message: 'No route found' });
});

app.listen(PORT, () => {
    // eslint-disable-next-line no-console
    console.log(`⚡ API Gateway running on http://localhost:${PORT}`);
});
