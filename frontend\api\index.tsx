import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

export const publicAPIClient = axios.create({
    baseURL: 'http://192.168.1.33:8080',
});

export const privateAPIClient = axios.create({
    baseURL: 'http://192.168.1.33:8080',
});

privateAPIClient.interceptors.request.use(async (config) => {
    const token = await SecureStore.getItemAsync('accessToken');
    const sessionID = await SecureStore.getItemAsync('sessionID');
    if (token) config.headers.Authorization = `Bearer ${token}`;
    if (sessionID) config.headers.Session = sessionID;
    return config;
});

privateAPIClient.interceptors.response.use(
    (res) => res,
    async (error) => {
        const originalRequest = error.config;
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            const sessionID = await SecureStore.getItemAsync('sessionID');

            try {
                const { data } = await publicAPIClient.post(
                    '/users/refresh',
                    null,
                    {
                        headers: {
                            Session: sessionID,
                        },
                    }
                );

                const { accessToken } = data;
                await SecureStore.setItemAsync('accessToken', accessToken);
                return privateAPIClient(originalRequest);
            } catch (e) {
                return Promise.reject(e);
            }
        }
        return Promise.reject(error);
    }
);

publicAPIClient.interceptors.request.use((config) => {
    return config;
});
