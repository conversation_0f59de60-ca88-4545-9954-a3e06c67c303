{"name": "admin-management-service", "version": "1.0.0", "main": "app.js", "scripts": {"start": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@build-connect/utils": "^1.0.2", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "express": "^5.1.0", "mongoose": "^8.15.0", "redis": "^5.1.0"}}