import React, { useState } from "react";
import { Modal, View, Text, TextInput, TouchableOpacity, Image, StyleSheet, ActivityIndicator } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { showToast } from "../../../utils/showToast";

const ProfileModal = ({ isVisible, onClose, userData, onSave, theme, isLoading }) => {
  const [formData, setFormData] = useState({
    name: userData.name || "User",
    email: userData.email || "",
    phone: userData.phone || "",
    role: userData.role || "",
    avatarUri: userData.avatarUri || null,
  });

  const isFormValid = () => {
    return formData.name.trim() && (!formData.email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email));
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSave = () => {
    if (!isFormValid()) {
      showToast("error", "Error", "Please enter a valid name and email");
      return;
    }
    onSave(formData);
    onClose();
  };

  const handleImagePick = async () => {
    try {
      const permission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permission.granted) {
        showToast("error", "Error", "Permission to access photos required");
        return;
      }
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });
      if (!result.canceled) {
        setFormData({ ...formData, avatarUri: result.assets[0].uri });
      }
    } catch (error) {
      showToast("error", "Error", "Failed to pick image");
    }
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent onRequestClose={onClose}>
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.overlayTouchable}
          onPress={onClose}
          accessibilityLabel="Close Profile Modal"
        />
        <View style={[styles.modalContainer, { backgroundColor: theme.CARD }]}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            accessibilityLabel="Close profile modal"
            accessibilityRole="button"
            testID="close-modal"
          >
            <Ionicons name="close" size={24} color={theme.PRIMARY} />
          </TouchableOpacity>

          <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>Profile Details</Text>

          <View style={styles.avatarContainer}>
            <View style={[styles.avatarWrapper, { backgroundColor: theme.PRIMARY }]}>
              {formData.avatarUri ? (
                <Image
                  source={{ uri: formData.avatarUri }}
                  style={styles.avatar}
                  accessibilityLabel={`Profile picture for ${formData.name}`}
                />
              ) : (
                <Text style={[styles.avatarText, { color: theme.WHITE }]}>
                  {formData.name.charAt(0).toUpperCase()}
                </Text>
              )}
            </View>
            <TouchableOpacity
              style={[styles.cameraButton, { backgroundColor: theme.GRAY_LIGHT_SEMITRANSPARENT }]}
              onPress={handleImagePick}
              accessibilityLabel="Change Profile Picture"
              accessibilityRole="button"
              testID="change-avatar"
            >
              <Ionicons name="camera-outline" size={20} color={theme.PRIMARY} />
            </TouchableOpacity>
          </View>

          <View style={styles.formContainer}>
            <View style={styles.inputWrapper}>
              <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>Name</Text>
              <TextInput
                style={[styles.input, {
                  borderColor: theme.INPUT_BORDER,
                  color: theme.TEXT_PRIMARY,
                  backgroundColor: theme.INPUT_BACKGROUND,
                }]}
                value={formData.name}
                onChangeText={(text) => handleInputChange("name", text)}
                placeholder="Enter name"
                placeholderTextColor={theme.TEXT_SECONDARY + "80"}
                accessibilityLabel="Name input"
              />
            </View>

            <View style={styles.inputWrapper}>
              <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>Email</Text>
              <TextInput
                style={[styles.input, {
                  borderColor: theme.INPUT_BORDER,
                  color: theme.TEXT_PRIMARY,
                  backgroundColor: theme.INPUT_BACKGROUND,
                }]}
                value={formData.email}
                onChangeText={(text) => handleInputChange("email", text)}
                placeholder="Enter email"
                placeholderTextColor={theme.TEXT_SECONDARY + "80"}
                keyboardType="email-address"
                accessibilityLabel="Email input"
              />
            </View>

            <View style={styles.inputWrapper}>
              <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>Phone</Text>
              <TextInput
                style={[styles.input, {
                  borderColor: theme.INPUT_BORDER,
                  color: theme.TEXT_PRIMARY,
                  backgroundColor: theme.INPUT_BACKGROUND,
                }]}
                value={formData.phone}
                onChangeText={(text) => handleInputChange("phone", text)}
                placeholder="Enter phone number"
                placeholderTextColor={theme.TEXT_SECONDARY + "80"}
                keyboardType="phone-pad"
                accessibilityLabel="Phone input"
              />
            </View>

            <View style={styles.inputWrapper}>
              <Text style={[styles.label, { color: theme.TEXT_SECONDARY }]}>Role</Text>
              <Text
                style={[styles.input, {
                  borderColor: theme.INPUT_BORDER,
                  color: theme.TEXT_SECONDARY,
                  backgroundColor: theme.INPUT_BACKGROUND_DISABLED || theme.INPUT_BACKGROUND + "80",
                }]}
              >
                {formData.role ? formData.role.charAt(0).toUpperCase() + formData.role.slice(1) : "N/A"}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: theme.PRIMARY, opacity: isLoading || !isFormValid() ? 0.6 : 1 }]}
            onPress={handleSave}
            disabled={isLoading || !isFormValid()}
            accessibilityLabel="Save Profile Changes"
            accessibilityRole="button"
            testID="save-profile"
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={theme.WHITE} />
            ) : (
              <Text style={[styles.saveButtonText, { color: theme.WHITE }]}>Save Changes</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  overlayTouchable: {
    position: "absolute",
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  modalContainer: {
    width: "90%",
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  closeButton: {
    position: "absolute",
    top: 16,
    right: 16,
    padding: 8,
    zIndex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 20,
  },
  avatarContainer: {
    alignItems: "center",
    marginBottom: 20,
    position: "relative",
  },
  avatarWrapper: {
    width: 90,
    height: 90,
    borderRadius: 45,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  avatar: {
    width: "100%",
    height: "100%",
  },
  avatarText: {
    fontSize: 36,
    fontWeight: "bold",
  },
  cameraButton: {
    position: "absolute",
    bottom: -10,
    right: -10,
    padding: 10,
    borderRadius: 30,
  },
  formContainer: {
    gap: 16,
  },
  inputWrapper: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
  },
  saveButton: {
    marginTop: 20,
    padding: 14,
    borderRadius: 12,
    alignItems: "center",
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ProfileModal;