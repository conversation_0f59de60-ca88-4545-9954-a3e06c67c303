import React, { useContext, useState } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    FlatList,
    Modal,
    ScrollView,
    Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';

const { width } = Dimensions.get('window');
const itemSize = (width - 48) / 3; // 3 items per row with padding

export default function PortfolioGrid({ portfolio = [] }) {
    const { theme } = useContext(ThemeContext);
    const [selectedItem, setSelectedItem] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);

    const openModal = (item) => {
        setSelectedItem(item);
        setModalVisible(true);
    };

    const closeModal = () => {
        setModalVisible(false);
        setSelectedItem(null);
    };

    const renderPortfolioItem = ({ item, index }) => (
        <TouchableOpacity
            style={[styles.portfolioItem, { backgroundColor: theme.CARD }]}
            onPress={() => openModal(item)}
            activeOpacity={0.7}
        >
            <Image
                source={{ uri: item.image }}
                style={styles.portfolioImage}
                resizeMode="cover"
            />
            <View style={styles.portfolioOverlay}>
                <Ionicons name="expand" size={20} color="white" />
            </View>
        </TouchableOpacity>
    );

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    if (!portfolio || portfolio.length === 0) {
        return (
            <View
                style={[styles.emptyContainer, { backgroundColor: theme.CARD }]}
            >
                <Ionicons
                    name="images-outline"
                    size={48}
                    color={theme.TEXT_SECONDARY}
                />
                <Text
                    style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}
                >
                    No portfolio items yet
                </Text>
                <Text
                    style={[
                        styles.emptySubtext,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    Portfolio items will appear here
                </Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                Portfolio ({portfolio.length})
            </Text>

            <FlatList
                data={portfolio}
                renderItem={renderPortfolioItem}
                keyExtractor={(item, index) => item._id || index.toString()}
                numColumns={3}
                scrollEnabled={false}
                contentContainerStyle={styles.grid}
                columnWrapperStyle={styles.row}
            />

            {/* Portfolio Detail Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={closeModal}
            >
                <View style={styles.modalOverlay}>
                    <View
                        style={[
                            styles.modalContent,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={closeModal}
                        >
                            <Ionicons
                                name="close"
                                size={24}
                                color={theme.TEXT_PRIMARY}
                            />
                        </TouchableOpacity>

                        <ScrollView showsVerticalScrollIndicator={false}>
                            {selectedItem && (
                                <>
                                    <Image
                                        source={{ uri: selectedItem.image }}
                                        style={styles.modalImage}
                                        resizeMode="cover"
                                    />

                                    <View style={styles.modalDetails}>
                                        <Text
                                            style={[
                                                styles.modalCaption,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            {selectedItem.caption}
                                        </Text>

                                        <View style={styles.modalMeta}>
                                            <Ionicons
                                                name="calendar-outline"
                                                size={16}
                                                color={theme.TEXT_SECONDARY}
                                            />
                                            <Text
                                                style={[
                                                    styles.modalDate,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                {formatDate(
                                                    selectedItem.createdAt
                                                )}
                                            </Text>
                                        </View>
                                    </View>
                                </>
                            )}
                        </ScrollView>
                    </View>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginHorizontal: 16,
        marginBottom: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    grid: {
        paddingBottom: 16,
    },
    row: {
        justifyContent: 'space-between',
        marginBottom: 4,
    },
    portfolioItem: {
        width: itemSize,
        height: itemSize,
        borderRadius: 8,
        overflow: 'hidden',
        position: 'relative',
    },
    portfolioImage: {
        width: '100%',
        height: '100%',
    },
    portfolioOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyContainer: {
        marginHorizontal: 16,
        marginBottom: 16,
        borderRadius: 12,
        padding: 32,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    emptyText: {
        fontSize: 16,
        fontWeight: '600',
        marginTop: 12,
        textAlign: 'center',
    },
    emptySubtext: {
        fontSize: 14,
        marginTop: 4,
        textAlign: 'center',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: width * 0.9,
        maxHeight: '80%',
        borderRadius: 16,
        overflow: 'hidden',
    },
    closeButton: {
        position: 'absolute',
        top: 16,
        right: 16,
        zIndex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        borderRadius: 20,
        width: 40,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalImage: {
        width: '100%',
        height: 300,
    },
    modalDetails: {
        padding: 20,
    },
    modalCaption: {
        fontSize: 16,
        lineHeight: 24,
        marginBottom: 12,
    },
    modalMeta: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    modalDate: {
        fontSize: 14,
        marginLeft: 6,
    },
});
