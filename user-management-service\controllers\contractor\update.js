const ExpressError = require('@build-connect/utils/ExpressError');
const { withTransaction } = require('@build-connect/utils/transaction');
const { Cloudinary } = require('../../cloudinary');
const Aadhaar = require('../../model/Aadhaar');
const PAN = require('../../model/Pan');
const Contractor = require('../../model/Contractor');
const Asset = require('../../model/Asset');
const { createAssetFromFile } = require('../assets');

exports.updateContractorApplication = async (req, res) => {
    const userId = req.user.id;
    const { contractorId } = req.params;

    const {
        aadhaarNumber,
        nameOnAadhaar,
        dateOfBirth,
        gender,
        address,
        panNumber,
        panName,
        panDateOfBirth,
        specialties,
        experience,
        serviceAreas,
    } = req.body;

    const result = await withTransaction(async (session) => {
        const contractorUpdate = await Contractor.updateOne(
            { _id: contractorId, user: userId },
            {
                $set: {
                    specialties,
                    experience,
                    serviceAreas,
                },
            },
            { session }
        );

        if (contractorUpdate.modifiedCount === 0) {
            throw new ExpressError('Contractor application not found', 404);
        }

        const aadhaarUpdate = await Aadhaar.findOneAndUpdate(
            { userId },
            {
                $set: {
                    aadhaarNumber,
                    nameOnAadhaar,
                    dateOfBirth: new Date(dateOfBirth),
                    gender,
                    address,
                },
            },
            { session, projection: { _id: 1 } }
        );

        if (!aadhaarUpdate) {
            throw new ExpressError('Aadhaar record not found', 404);
        }

        if (req.files?.aadhaarDocument?.[0]) {
            const oldAadhaarAsset = await Asset.findOneAndDelete({
                entityId: aadhaarUpdate._id.toString(),
                entityType: 'Aadhaar',
            }).session(session);

            if (oldAadhaarAsset) {
                const { result: deleteResult } =
                    await Cloudinary.getCloudinary().uploader.destroy(
                        oldAadhaarAsset.fileName
                    );

                if (deleteResult !== 'ok' && deleteResult !== 'not found') {
                    throw new ExpressError(
                        'Failed to delete Aadhaar asset',
                        500
                    );
                }
            }

            await createAssetFromFile(
                req.files.aadhaarDocument[0],
                aadhaarUpdate._id,
                'Aadhaar',
                'aadhar',
                session
            );
        }

        const panUpdate = await PAN.findOneAndUpdate(
            { userId },
            {
                $set: {
                    panNumber,
                    panName,
                    dateOfBirth: new Date(panDateOfBirth),
                },
            },
            { session, projection: { _id: 1 } }
        );

        if (!panUpdate) {
            throw new ExpressError('PAN record not found', 404);
        }

        if (req.files?.panDocument?.[0]) {
            const oldPanAsset = await Asset.findOneAndDelete({
                entityId: panUpdate._id.toString(),
                entityType: 'PAN',
            }).session(session);

            if (oldPanAsset) {
                const { result: deleteResult } =
                    await Cloudinary.getCloudinary().uploader.destroy(
                        oldPanAsset.fileName
                    );

                if (deleteResult !== 'ok' && deleteResult !== 'not found') {
                    throw new ExpressError('Failed to delete PAN asset', 500);
                }
            }

            await createAssetFromFile(
                req.files.panDocument[0],
                panUpdate._id,
                'PAN',
                'pan',
                session
            );
        }

        return { contractorId };
    });

    res.status(200).json({
        message: 'Contractor application updated successfully',
        applicationId: result.contractorId,
    });
};
