const ServiceRequest = require('../../model/serviceRequest');

exports.createServiceRequest = async (req, res) => {
    const userId = req.user.id;
    const { recipientId } = req.body;
    const { projectId } = req.params;

    const serviceRequest = new ServiceRequest({
        userId,
        recipientId,
        projectId,
    });

    await serviceRequest.save();

    res.status(201).json({
        message: 'Service request created successfully',
    });
};
