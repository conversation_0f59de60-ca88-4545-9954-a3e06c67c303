const ExpressError = require('@build-connect/utils/ExpressError');
const path = require('path');
const Asset = require('../model/Asset');

const getFileType = (file) => {
    if (file.mimetype) {
        const mimeType = file.mimetype.toLowerCase();
        if (mimeType.includes('jpeg') || mimeType.includes('jpg'))
            return 'jpeg';
        if (mimeType.includes('png')) return 'png';
        if (mimeType.includes('pdf')) return 'pdf';
    }

    const ext = path.extname(file.name || file.filename || '').toLowerCase();
    switch (ext) {
        case '.jpg':
        case '.jpeg':
            return 'jpeg';
        case '.png':
            return 'png';
        case '.pdf':
            return 'pdf';
        default:
            return 'jpeg';
    }
};

exports.createAsset = async (req, res) => {
    const { entityId, entityType, assetType } = req.body;

    if (!req.file) {
        throw new ExpressError('No file uploaded', 400);
    }

    if (!entityId || !entityType || !assetType) {
        throw new ExpressError(
            'entityID, entityType, and assetType are required',
            400
        );
    }

    const fileType = getFileType(req.file);

    const asset = new Asset({
        imageURL: req.file.path,
        entityId,
        entityType,
        assetType,
        fileType,
        fileName: req.file.filename,
    });

    await asset.save();

    res.status(201).json({
        message: 'Asset created successfully',
        asset,
    });
};

exports.getAsset = async (req, res) => {
    const { assetId } = req.params;

    const asset = await Asset.findById(assetId);
    if (!asset) {
        throw new ExpressError('Asset not found', 404);
    }

    res.status(200).json({
        message: 'Asset retrieved successfully',
        asset,
    });
};

exports.getAssetsByEntity = async (req, res) => {
    const { entityId, entityType, assetType } = req.query;

    if (!entityId || !entityType) {
        throw new ExpressError('entityID and entityType are required', 400);
    }

    const filter = { entityId, entityType };
    if (assetType) {
        filter.assetType = assetType;
    }

    const assets = await Asset.find(filter);

    res.status(200).json({
        message: 'Assets retrieved successfully',
        assets,
    });
};

exports.createAssetFromFile = async (
    file,
    entityId,
    entityType,
    assetType,
    session = null
) => {
    if (!file || !file.path) {
        return null;
    }

    const fileType = getFileType(file);

    const asset = new Asset({
        imageURL: file.path,
        entityId,
        entityType,
        assetType,
        fileType,
        fileName: file.filename,
    });

    await asset.save({ session });
    return asset;
};
