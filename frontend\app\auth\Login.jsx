import {
    View,
    Text,
    Image,
    TouchableOpacity,
    Pressable,
    TextInput,
    StyleSheet,
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState, useContext } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { privateAPIClient, publicAPIClient } from '../../api';
import * as SecureStore from 'expo-secure-store';
import BackButton from '../Components/Shared/BackButton';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { height } = Dimensions.get('window');

export default function Login() {
    const { theme } = useContext(ThemeContext);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState({ email: '', password: '' });

    const router = useRouter();

    // Email validation function
    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    // Form validation function
    const validateForm = () => {
        let isValid = true;
        const newErrors = { email: '', password: '' };

        // Email validation
        if (!email.trim()) {
            newErrors.email = 'Email is required';
            isValid = false;
        } else if (!validateEmail(email)) {
            newErrors.email = 'Please enter a valid email';
            isValid = false;
        }

        // Password validation
        if (!password) {
            newErrors.password = 'Password is required';
            isValid = false;
        } else if (password.length < 6) {
            newErrors.password = 'Password must be at least 6 characters';
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async () => {
        // Validate form before submission
        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        const data = {
            email,
            password,
        };

        try {
            let res = await publicAPIClient.post(
                '/user-service/api/v1/login',
                data
            );
            await SecureStore.setItemAsync('accessToken', res.data.accessToken);
            await SecureStore.setItemAsync('sessionID', res.data.sessionId);
            res = await privateAPIClient.get(
                '/user-service/api/v1/user/profile'
            );
            showToast('success', 'Login Successful', 'Welcome back!');
            router.push('/(tabs)/Home');
        } catch (e) {
            // Handle specific error cases
            if (e.response) {
                if (e.response.status === 401) {
                    showToast(
                        'error',
                        'Login Failed',
                        'Invalid email or password. Please try again.',
                        'OK',
                        '/auth/Login'
                    );
                } else if (e.response.status === 429) {
                    showToast(
                        'error',
                        'Login Failed',
                        'Too many attempts. Please try again later.',
                        'OK',
                        '/auth/Login'
                    );
                } else {
                    showToast(
                        'error',
                        'Login Error',
                        'An error occurred during login. Please try again.',
                        'OK',
                        '/auth/Login'
                    );
                }
            } else if (e.request) {
                showToast(
                    'error',
                    'Network Error',
                    'Please check your internet connection and try again.',
                    'OK',
                    '/auth/Login'
                );
            } else {
                showToast(
                    'error',
                    'Error',
                    'An unexpected error occurred. Please try again.'
                );
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Logo */}
                    <View style={styles.logoContainer}>
                        <Image
                            source={require('../../assets/images/build-connect.jpg')}
                            style={[
                                styles.logo,
                                { borderColor: theme.LOGO_BORDER },
                            ]}
                            resizeMode="contain"
                        />
                    </View>

                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                backgroundColor: theme.CARD,
                                shadowColor: theme.SHADOW,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Welcome Back
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Sign in to continue
                        </Text>

                        {/* Email Input */}
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    borderColor: theme.INPUT_BORDER,
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                },
                                errors.email ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="mail-outline"
                                size={22}
                                color={errors.email ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Email"
                                value={email}
                                onChangeText={(text) => {
                                    setEmail(text);
                                    if (errors.email) {
                                        setErrors({ ...errors, email: '' });
                                    }
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                autoCapitalize="none"
                                keyboardType="email-address"
                                autoComplete="email"
                            />
                            {email.length > 0 && (
                                <TouchableOpacity
                                    onPress={() => setEmail('')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.email ? (
                            <Text style={styles.errorText}>{errors.email}</Text>
                        ) : null}

                        {/* Password Input */}
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    borderColor: theme.INPUT_BORDER,
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                },
                                errors.password ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="lock-closed-outline"
                                size={22}
                                color={errors.password ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Password"
                                value={password}
                                onChangeText={(text) => {
                                    setPassword(text);
                                    if (errors.password) {
                                        setErrors({ ...errors, password: '' });
                                    }
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                secureTextEntry={!showPassword}
                            />
                            <TouchableOpacity
                                onPress={() => setShowPassword(!showPassword)}
                                style={styles.passwordToggle}
                            >
                                <Ionicons
                                    name={
                                        showPassword
                                            ? 'eye-off-outline'
                                            : 'eye-outline'
                                    }
                                    size={22}
                                    color={
                                        errors.password ? 'red' : theme.PRIMARY
                                    }
                                />
                            </TouchableOpacity>
                        </View>
                        {errors.password ? (
                            <Text style={styles.errorText}>
                                {errors.password}
                            </Text>
                        ) : null}

                        {/* Forgot Password */}
                        <TouchableOpacity
                            onPress={() => router.push('/auth/ForgotPassword')}
                            style={styles.forgotPasswordContainer}
                        >
                            <Text
                                style={[
                                    styles.forgotPasswordText,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                Forgot Password?
                            </Text>
                        </TouchableOpacity>

                        {/* Login Button */}
                        <TouchableOpacity
                            onPress={handleSubmit}
                            style={[
                                styles.loginButton,
                                { shadowColor: theme.PRIMARY },
                            ]}
                            disabled={isLoading}
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={styles.loginButtonGradient}
                            >
                                {isLoading ? (
                                    <View style={styles.loadingContainer}>
                                        <ActivityIndicator
                                            color="white"
                                            size="small"
                                        />
                                        <Text style={styles.loginButtonText}>
                                            Logging in...
                                        </Text>
                                    </View>
                                ) : (
                                    <Text style={styles.loginButtonText}>
                                        Login
                                    </Text>
                                )}
                            </LinearGradient>
                        </TouchableOpacity>

                        {/* Sign Up Link */}
                        <View style={styles.signupContainer}>
                            <Text
                                style={[
                                    styles.signupText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Don't have an account?
                            </Text>
                            <Pressable
                                onPress={() => router.push('/auth/SignUp')}
                            >
                                <Text
                                    style={[
                                        styles.signupLink,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Create new Here
                                </Text>
                            </Pressable>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
    },
    logoContainer: {
        alignItems: 'center',
        marginTop: height * 0.01,
        marginBottom: height * 0.04,
    },
    logo: {
        width: 120,
        height: 120,
        borderWidth: 3,
        borderRadius: 60,
    },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        paddingHorizontal: 12,
        height: 56,
    },
    inputError: {
        borderColor: 'red',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginBottom: 8,
        marginLeft: 4,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    clearButton: {
        padding: 8,
    },
    passwordToggle: {
        padding: 8,
    },
    forgotPasswordContainer: {
        alignSelf: 'flex-end',
        marginBottom: 24,
        marginTop: 8,
    },
    forgotPasswordText: {
        fontSize: 14,
    },
    loginButton: {
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 14,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    loginButtonGradient: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    loginButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    signupContainer: {
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
    },
    signupText: {
        fontSize: 14,
        marginRight: 4,
    },
    signupLink: {
        fontSize: 14,
        fontWeight: 'bold',
    },
});
