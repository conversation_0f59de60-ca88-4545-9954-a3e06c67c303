const KafkaClient = require('@build-connect/utils/pubsub');
const { ClientIDs } = require('@build-connect/utils/constants');
const { Topics } = require('@build-connect/utils/constants/topics');
const VerificationRequests = require('../model/verificationRequests');
const KafkaBrokers = require('../config').get('KAFKA_BROKERS').split(',');

const Kafka = new KafkaClient(KafkaBrokers, ClientIDs.UserService);

const handleVerificationRequested = async ({ value }) => {
    const jsonValue = JSON.parse(value);
    const verificationRequest = new VerificationRequests({
        type: jsonValue.type,
        requesterId: jsonValue.requesterId,
        status: 'pending',
    });

    await verificationRequest.save();
};

const handlersMap = {
    [Topics.VERFICATION_REQUESTED]: handleVerificationRequested,
};

module.exports = {
    Kafka,
    handlersMap,
};
