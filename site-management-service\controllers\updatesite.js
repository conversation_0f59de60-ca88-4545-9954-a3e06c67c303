const ExpressError = require('@build-connect/utils/ExpressError');
const { withTransaction } = require('@build-connect/utils/transaction');
const { Cloudinary } = require('../cloudinary');
const Site = require('../model/site');
const EncumbranceCertificate = require('../model/encumbranceCertificate');
const PropertyTaxReceipt = require('../model/propertyTaxRecipt');
const Asset = require('../model/Asset');
const { createAssetsHandler } = require('./assetscontroller');

exports.updateSite = async (req, res) => {
    const siteId = req.params.id;
    const userId = req.user.id;

    const {
        name,
        addressLine1,
        addressLine2,
        landmark,
        location,
        pincode,
        state,
        district,
        plotArea,
        price,
        latitude,
        longitude,
        encOwnerName,
        encDocumentNo,
        surveyNo,
        village,
        subDistrict,
        District,
        ptrOwnerName,
        ptrReciptNo,
    } = req.body;

    const {
        siteImages = [],
        encumbranceCertificate = [],
        propertyTaxReceipt = [],
    } = req.files || {};

    await withTransaction(async (session) => {
        const { modifiedCount } = await Site.updateOne(
            { _id: siteId, userId: userId },
            {
                $set: {
                    name,
                    addressLine1,
                    addressLine2,
                    landmark,
                    location,
                    pincode,
                    state,
                    district,
                    plotArea,
                    price,
                    latitude,
                    longitude,
                    status: 'pending',
                },
            },
            { session }
        );
        if (!modifiedCount) {
            throw new ExpressError('Site not found or not owned by user', 404);
        }
        if (siteImages.length) {
            await createAssetsHandler({
                files: siteImages,
                entityId: siteId,
                assetType: 'site_image',
                session,
            });
        }

        const encDocument = await EncumbranceCertificate.findOneAndUpdate(
            { entityId: siteId },
            {
                $set: {
                    encOwnerName,
                    encDocumentNo,
                    surveyNo,
                    village,
                    subDistrict,
                    District,
                },
            },
            {
                session,
            }
        );

        if (encumbranceCertificate[0]) {
            const encAsset = await Asset.findOneAndDelete({
                entityId: encDocument._id,
                assetType: 'encumbrance_certificate',
            }).session(session);

            const { result } =
                await Cloudinary.getCloudinary().uploader.destroy(
                    encAsset.fileName
                );

            if (result !== 'ok' && result !== 'not found') {
                throw new ExpressError('something went wrong', 500);
            }

            await createAssetsHandler({
                files: [encumbranceCertificate[0]],
                entityId: encDocument._id,
                assetType: 'encumbrance_certificate',
                session,
            });
        }

        const ptrData = ptrOwnerName && ptrReciptNo && propertyTaxReceipt?.[0];

        const ptReciept = await PropertyTaxReceipt.findOneAndUpdate(
            { entityId: siteId },
            {
                $set: {
                    ptrOwnerName,
                    ptrReciptNo,
                },
            },
            { session }
        );

        if (ptReciept) {
            if (propertyTaxReceipt?.[0]) {
                const ptrAsset = await Asset.findOneAndDelete({
                    entityId: ptReciept.id,
                    assetType: 'property_tax_receipt',
                }).session(session);

                const { result } =
                    await Cloudinary.getCloudinary().uploader.destroy(
                        ptrAsset.fileName
                    );

                if (result !== 'ok' && result !== 'not found') {
                    throw new ExpressError('something went wrong', 500);
                }

                await createAssetsHandler({
                    files: [propertyTaxReceipt[0]],
                    entityId: ptReciept.id,
                    assetType: 'property_tax_receipt',
                    session,
                });
            }
        } else {
            if (!ptrData) {
                throw new ExpressError('all the fields are required', 400);
            }

            const [newPTR] = await PropertyTaxReceipt.create(
                [{ entityId: siteId, ptrOwnerName, ptrReciptNo }],
                { session }
            );

            await createAssetsHandler({
                files: [propertyTaxReceipt[0]],
                entityId: newPTR.id,
                assetType: 'property_tax_receipt',
                session,
            });
        }

        res.status(200).json({ message: 'Site updated successfully' });
    });
};
