const mongoose = require('mongoose');

const userSchema = new mongoose.Schema(
    {
        // Basic user fields
        name: {
            type: String,
            required: true,
            trim: true,
        },
        email: {
            type: String,
            required: true,
            unique: true,
            trim: true,
        },
        phone: {
            type: String,
            required: true,
            unique: true,
            trim: true,
        },
        password: {
            type: String,
            required: true,
            minlength: 6,
        },
        role: {
            type: String,
            enum: ['user', 'contractor', 'broker'],
            default: 'user',
        },
        location: {
            type: [String],
        },
        isAvailable: {
            type: Boolean,
            default: true,
        },
        isEmailVerified: {
            type: Boolean,
            default: false,
        },
        isPhoneVerified: {
            type: Boolean,
            default: false,
        },
        partnershipRequest: {
            type: String,
            enum: ['NONE', 'Broker', 'Contractor'],
            default: 'NONE',
        },
    },
    {
        timestamps: true,
    }
);

const User = mongoose.model('User', userSchema);
module.exports = User;
