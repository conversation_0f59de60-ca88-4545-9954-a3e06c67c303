name: build-connect-frontend
on: [push, workflow_dispatch]

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

permissions:
    contents: read

jobs:
    lint:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3

            - name: Use Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 18

            - name: Cache npm
              uses: actions/cache@v3
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci
              working-directory: frontend

            - name: Run ESLint
              run: npm run lint
              working-directory: frontend

            - name: Run Prettier Check
              run: npm run prettier:check
              working-directory: frontend
