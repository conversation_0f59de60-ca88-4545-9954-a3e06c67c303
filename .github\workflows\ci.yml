name: build-connect
on: [push, workflow_dispatch]

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

permissions:
    contents: read

jobs:
    lint:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3

            - name: Use Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 20

            - name: Install dependencies
              run: npm ci

            - name: Run ESLint
              run: npm run lint

            - name: Run Prettier Check
              run: npm run prettier:check
