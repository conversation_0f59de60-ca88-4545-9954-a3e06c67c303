const ExpressError = require('@build-connect/utils/ExpressError');
const { withTransaction } = require('@build-connect/utils/transaction');
const UserRequirement = require('../../model/projectRequirement');
const Project = require('../../model/project');

exports.updateProject = async (req, res) => {
    const userId = req.user.id;
    const { projectId } = req.params;

    const {
        projectName,
        projectType,
        location,
        constructionType,
        expectedStartDate,
        expectedCompletionDate,
        budget,
        designPreferences,
        additionalFacilities,
        brokerAssistanceRequired,
        specialInstructions,
    } = req.body;

    await withTransaction(async (session) => {
        const projectUpdate = await Project.updateOne(
            { _id: projectId, userId },
            {
                $set: {
                    projectName,
                },
            },
            { session }
        );

        if (projectUpdate.modifiedCount === 0) {
            throw new ExpressError('project not found', 404);
        }

        const requirementUpdate = await UserRequirement.updateOne(
            { projectId, userId },
            {
                $set: {
                    projectType,
                    location,
                    constructionType,
                    expectedStartDate: expectedStartDate
                        ? new Date(expectedStartDate)
                        : undefined,
                    expectedCompletionDate: expectedCompletionDate
                        ? new Date(expectedCompletionDate)
                        : undefined,
                    budget,
                    designPreferences,
                    additionalFacilities,
                    brokerAssistanceRequired,
                    specialInstructions,
                },
            },
            { session }
        );

        if (requirementUpdate.modifiedCount === 0) {
            throw new ExpressError('requirements record not found', 404);
        }
    });

    res.status(200).json({
        message: 'Project and Requirement updated successfully',
    });
};
