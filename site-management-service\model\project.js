const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },

        projectName: {
            type: String,
            required: true,
            trim: true,
        },

        contractorId: {
            type: mongoose.Schema.Types.ObjectId,
            default: null,
        },

        brokerId: {
            type: mongoose.Schema.Types.ObjectId,
            default: null,
        },

        status: {
            type: String,
            enum: [
                'Initiated',
                'Planning',
                'In Progress',
                'On Hold',
                'Completed',
                'Cancelled',
            ],
            default: 'Initiated',
        },

        actualStartDate: Date,
        actualEndDate: Date,

        progressLogs: [
            {
                date: { type: Date, default: Date.now },
                stage: {
                    type: String,
                    required: true,
                },
                description: String,
                addedById: {
                    type: mongoose.Schema.Types.ObjectId,
                    required: true,
                },
                addedByRole: {
                    type: String,
                    enum: ['Contractor', 'Broker'],
                    required: true,
                },
            },
        ],

        remarks: String,
    },
    { timestamps: true }
);

module.exports = mongoose.model('Project', projectSchema);
