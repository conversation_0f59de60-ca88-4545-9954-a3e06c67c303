const { withTransaction } = require('@build-connect/utils/transaction');
const ExpressError = require('@build-connect/utils/ExpressError');
const ServiceRequest = require('../../model/serviceRequest');
const Project = require('../../model/project');

exports.updateServiceRequest = async (req, res) => {
    const recipientId = req.user.id;
    const userRole = req.user.role;
    const projectId = req.params.projectId;
    const { status } = req.body;

    await withTransaction(async (session) => {
        const project = await Project.findOne({ _id: projectId }).session(
            session
        );
        if (!project) {
            throw new ExpressError('Project not found', 404);
        }

        if (status === 'accepted') {
            let updated = false;
            if (userRole === 'broker' && !project.brokerId) {
                project.brokerId = recipientId;
                updated = true;
            } else if (userRole === 'contractor' && !project.contractorId) {
                project.contractorId = recipientId;
                updated = true;
            }

            if (updated) {
                await project.save({ session });
            }
        }

        const deleteResult = await ServiceRequest.deleteOne({
            recipientId,
            projectId,
        }).session(session);

        if (deleteResult.deletedCount === 0) {
            throw new ExpressError('Service request not found', 404);
        }
    });

    res.status(200).json({
        message:
            status === 'accepted'
                ? 'Service request accepted'
                : 'Service request rejected',
    });
};
