const mongoose = require('mongoose');

const panSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true,
        },
        panNumber: {
            type: String,
            required: true,
            unique: true,
            uppercase: true,
            match: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
        },
        panName: {
            type: String,
            required: true,
            trim: true,
        },
        dateOfBirth: {
            type: Date,
            required: true,
        },
    },
    { timestamps: true }
);

module.exports = mongoose.model('PAN', panSchema);
