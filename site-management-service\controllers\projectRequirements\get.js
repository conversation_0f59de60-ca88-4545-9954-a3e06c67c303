const ExpressError = require('@build-connect/utils/ExpressError');
const Project = require('../../model/project');
const UserRequirement = require('../../model/projectRequirement');

exports.getProjectById = async (req, res) => {
    const { projectId } = req.params;
    const project = await Project.findById(projectId).lean();
    if (!project) {
        throw new ExpressError('Project not found', 404);
    }

    const requirement = await UserRequirement.findOne({ projectId }).lean();
    if (!requirement) {
        throw new ExpressError('requirement not found', 404);
    }
    res.status(200).json({ project, requirement });
};

exports.getProjects = async (req, res) => {
    const userId = req.user.id;
    const projects = await Project.find({ userId }).lean();
    res.status(200).json({ projects });
};
