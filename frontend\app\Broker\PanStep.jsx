import React, { useState } from "react";
import { View, Text, TextInput, TouchableOpacity, Pressable } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import ModalDatePicker from "react-native-modal-datetime-picker";
import * as DocumentPicker from "expo-document-picker";
import { styles } from "./styles";
import { showToast } from "../../utils/showToast";
import { normalizePan, getFileNameFromUrl } from "../../utils/BrokerFormUtils";

const PanStep = ({ formik, theme, setStep, handlePreviewDocument }) => {
  const [showPanDatePicker, setShowPanDatePicker] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const pickDocument = async () => {
    setIsUploading(true);
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ["image/jpeg", "image/png", "application/pdf"],
      });
      if (!result.canceled && result.assets?.[0]) {
        const file = result.assets[0];
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
        const validExtensions = [".jpg", ".jpeg", ".png", ".pdf"];
        if (file.size > MAX_FILE_SIZE) {
          showToast("error", "File Size Error", "File size exceeds the limit of 5MB.");
          return;
        }
        if (!validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
          showToast("error", "File Type Error", "Only JPEG, PNG, and PDF are allowed.");
          return;
        }
        formik.setFieldValue("panDocument", file);
      }
    } catch (error) {
      showToast("error", "Document Picker Error", "Failed to select file");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <>
      <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>PAN Details</Text>
      {/* PAN Name */}
      <View
        style={[
          styles.inputContainer,
          { backgroundColor: theme.INPUT_BACKGROUND, borderColor: theme.INPUT_BORDER },
          formik.touched.panName && formik.errors.panName ? styles.inputError : null,
        ]}
      >
        <Ionicons
          name="person-outline"
          size={22}
          color={formik.touched.panName && formik.errors.panName ? "red" : theme.PRIMARY}
          style={styles.inputIcon}
        />
        <TextInput
          placeholder="Name on PAN"
          value={formik.values.panName}
          onChangeText={formik.handleChange("panName")}
          onBlur={formik.handleBlur("panName")}
          placeholderTextColor={theme.TEXT_PLACEHOLDER}
          style={[styles.input, { color: theme.TEXT_PRIMARY }]}
          accessibilityLabel="Enter name on PAN"
          testID="panName-input"
        />
        {formik.values.panName.length > 0 && (
          <TouchableOpacity
            onPress={() => formik.setFieldValue("panName", "")}
            style={styles.clearButton}
          >
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>
      {formik.touched.panName && formik.errors.panName && (
        <Text style={styles.errorText}>{formik.errors.panName}</Text>
      )}

      {/* PAN Number */}
      <View
        style={[
          styles.inputContainer,
          { backgroundColor: theme.INPUT_BACKGROUND, borderColor: theme.INPUT_BORDER },
          formik.touched.panNumber && formik.errors.panNumber ? styles.inputError : null,
        ]}
      >
        <MaterialCommunityIcons
          name="card-account-details-outline"
          size={22}
          color={formik.touched.panNumber && formik.errors.panNumber ? "red" : theme.PRIMARY}
          style={styles.inputIcon}
        />
        <TextInput
          placeholder="PAN Number"
          value={formik.values.panNumber}
          onChangeText={(text) => formik.setFieldValue("panNumber", normalizePan(text))}
          onBlur={formik.handleBlur("panNumber")}
          placeholderTextColor={theme.TEXT_PLACEHOLDER}
          style={[styles.input, { color: theme.TEXT_PRIMARY }]}
          maxLength={10}
          autoCapitalize="characters"
          accessibilityLabel="Enter PAN number"
          testID="panNumber-input"
        />
        {formik.values.panNumber.length > 0 && (
          <TouchableOpacity
            onPress={() => formik.setFieldValue("panNumber", "")}
            style={styles.clearButton}
          >
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>
      {formik.touched.panNumber && formik.errors.panNumber && (
        <Text style={styles.errorText}>{formik.errors.panNumber}</Text>
      )}

      {/* PAN Date of Birth */}
      <Pressable
        onPress={() => setShowPanDatePicker(true)}
        style={[
          styles.inputContainer,
          { backgroundColor: theme.INPUT_BACKGROUND, borderColor: theme.INPUT_BORDER },
          formik.touched.panDateOfBirth && formik.errors.panDateOfBirth ? styles.inputError : null,
        ]}
      >
        <Ionicons
          name="calendar-outline"
          size={22}
          color={formik.touched.panDateOfBirth && formik.errors.panDateOfBirth ? "red" : theme.PRIMARY}
          style={styles.inputIcon}
        />
        <Text style={[styles.dateText, { color: formik.values.panDateOfBirth? theme.TEXT_PRIMARY : theme.TEXT_PLACEHOLDER }]}>
          {formik.values.panDateOfBirth
            ? new Date(formik.values.panDateOfBirth).toLocaleDateString()
            : "Date of Birth"}
        </Text>
      </Pressable>
      <ModalDatePicker
        isVisible={showPanDatePicker}
        mode="date"
        date={formik.values.panDateOfBirth || new Date()}
        onConfirm={(date) => {
          formik.setFieldValue("panDateOfBirth", date);
          setShowPanDatePicker(false);
        }}
        onCancel={() => setShowPanDatePicker(false)}
        maximumDate={new Date()}
        testID="panDatePicker"
      />
      {formik.touched.panDateOfBirth && formik.errors.panDateOfBirth && (
        <Text style={styles.errorText}>{formik.errors.panDateOfBirth}</Text>
      )}

      {/* PAN Document */}
      <TouchableOpacity
        onPress={pickDocument}
        disabled={isUploading}
        style={[
          styles.fileButton,
          { backgroundColor: theme.INPUT_BACKGROUND, borderColor: theme.INPUT_BORDER, opacity: isUploading ? 0.6 : 1 },
          formik.touched.panDocument && formik.errors.panDocument ? styles.inputError : null,
        ]}
      >
        <Ionicons name="cloud-upload-outline" size={22} color={theme.PRIMARY} style={styles.inputIcon} />
        <Text style={[styles.fileButtonText, { color: formik.values.panDocument? theme.TEXT_PRIMARY : theme.TEXT_PLACEHOLDER }]}>
          {formik.values.panDocument
            ? typeof formik.values.panDocument === "string"
              ? getFileNameFromUrl(formik.values.panDocument)
              : formik.values.panDocument.name
            : "Upload PAN Document"}
        </Text>
        {formik.values.panDocument && (
          <TouchableOpacity
            onPress={() => handlePreviewDocument(formik.values.panDocument)}
            style={styles.previewButton}
          >
            <Ionicons name="eye-outline" size={22} color={theme.GRAY} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
      {formik.touched.panDocument && formik.errors.panDocument && (
        <Text style={styles.errorText}>{formik.errors.panDocument}</Text>
      )}

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.submitButton, { flex: 1, marginRight: 8, borderColor: theme.PRIMARY }]}
          onPress={() => setStep("aadhaar")}
          accessibilityLabel="Go back to Aadhaar details"
          accessibilityRole="button"
        >
          <LinearGradient
            colors={[theme.WHITE, theme.GRAY_LIGHT]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.submitButtonGradient}
          >
            <Text style={[styles.submitButtonText, { color: theme.PRIMARY }]}>Back</Text>
          </LinearGradient>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.submitButton, { flex: 1, marginLeft: 8, borderColor: theme.ACCENT }]}
          onPress={async () => {
            const errors = await formik.validateForm();
            const fields = ["panNumber", "panName", "panDateOfBirth", "panDocument"];
            if (!fields.some((field) => errors[field])) {
              setStep("brokerDetails");
            } else {
              fields.forEach((field) => formik.setFieldTouched(field, true));
            }
          }}
          accessibilityLabel="Proceed to broker details"
          accessibilityRole="button"
        >
          <LinearGradient
            colors={[theme.PRIMARY, theme.SECONDARY]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.submitButtonGradient}
          >
            <Text style={[styles.submitButtonText, { color: theme.WHITE }]}>Next</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </>
  );
};

export default PanStep;