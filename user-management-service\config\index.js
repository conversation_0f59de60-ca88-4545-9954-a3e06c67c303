require('dotenv').config();

class EnvConfig {
    constructor() {
        this.requiredVars = {
            CLOUDINARY_CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME,
            CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,
            CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,
            JWT_ACCESS_KEY: process.env.JWT_ACCESS_KEY,
            JWT_REFRESH_KEY: process.env.JWT_REFRESH_KEY,
            DATABASE_URL: process.env.DATABASE_URL,
            PORT: process.env.PORT || 3007,
            KAFKA_BROKERS: process.env.KAFKA_BROKERS || 'localhost:9092',
        };

        this._validate();
    }

    _validate() {
        const missing = Object.entries(this.requiredVars)
            .filter(([_, value]) => !value)
            .map(([key]) => key);

        if (missing.length > 0) {
            console.error(
                `❌ Missing required environment variables:\n${missing.join('\n')}`
            );
            process.exit(1);
        }
    }

    get(key) {
        return this.requiredVars[key];
    }

    getAll() {
        return { ...this.requiredVars };
    }
}

const env = new EnvConfig();

module.exports = env;
