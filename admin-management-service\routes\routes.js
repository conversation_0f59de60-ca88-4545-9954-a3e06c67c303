const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const { getTickets } = require('../controllers/getadmins');
const {
    getVerificationRequests,
    getVerificationRequest,
} = require('../controllers/verification/list');
const { client } = require('../cache');

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, catchAsync(getTickets));

// Verification routes
router.get(
    '/verifications',
    doAuthenticate,
    catchAsync(getVerificationRequests)
);
router.get(
    '/verifications/:id',
    doAuthenticate,
    catchAsync(getVerificationRequest)
);

module.exports = router;
