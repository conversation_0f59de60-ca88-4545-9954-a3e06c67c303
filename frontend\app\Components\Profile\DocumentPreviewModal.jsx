import React, { useContext } from 'react';
import {
    View,
    Text,
    Image,
    Modal,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';

const { width, height } = Dimensions.get('window');

export default function DocumentPreviewModal({
    visible,
    documentUrl,
    documentType,
    onClose,
}) {
    const { theme } = useContext(ThemeContext);
    const isImage =
        documentUrl &&
        (documentUrl.endsWith('.png') ||
            documentUrl.endsWith('.jpg') ||
            documentUrl.endsWith('.jpeg'));
    const isValidUrl = documentUrl && documentUrl.startsWith('http');

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
      accessibilityLabel={`Preview ${documentType} document`}
    >
      <View style={[styles.modalContainer, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}>
        <View style={[styles.modalContent, { backgroundColor: theme.CARD || theme.WHITE }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
              {documentType} Preview
            </Text>
            <TouchableOpacity
              onPress={onClose}
              accessibilityLabel="Close preview modal"
              accessibilityRole="button"
            >
              <Ionicons name="close" size={24} color={theme.TEXT_PRIMARY} />
            </TouchableOpacity>
          </View>
          {  isImage ? (
            <Image
              source={{ uri: documentUrl }}
              style={styles.previewImage}
              resizeMode="contain"
              accessibilityLabel={`${documentType} document image`}
            />
          ) : (
            <View style={styles.placeholderContainer}>
              <Ionicons name="document-text-outline" size={48} color={theme.TEXT_SECONDARY} />
              <Text style={[styles.placeholderText, { color: theme.TEXT_SECONDARY }]}>
                {true ? 'PDF Preview Not Supported' : 'Invalid or Missing Document URL'}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: width * 0.9,
        maxWidth: 400,
        borderRadius: 12,
        padding: 16,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    previewImage: {
        width: '100%',
        height: height * 0.4,
        borderRadius: 8,
    },
    placeholderContainer: {
        height: height * 0.4,
        justifyContent: 'center',
        alignItems: 'center',
    },
    placeholderText: {
        fontSize: 16,
        marginTop: 8,
        textAlign: 'center',
    },
});
