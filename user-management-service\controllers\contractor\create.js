const ExpressError = require('@build-connect/utils/ExpressError');
const { Topics } = require('@build-connect/utils/constants/topics');
const { withTransaction } = require('@build-connect/utils/transaction');
const Aadhaar = require('../../model/Aadhaar');
const PAN = require('../../model/Pan');
const Contractor = require('../../model/Contractor');
const User = require('../../model/user');
const { createAssetFromFile } = require('../assets');
const { Kafka } = require('../../pubsub');

exports.createContractorApplication = async (req, res) => {
    const userId = req.user.id;

    if (
        req.user.partnershipRequest === 'Broker' ||
        req.user.partnershipRequest === 'Contractor'
    ) {
        throw new ExpressError('Partnership request already submitted.', 403);
    }

    const {
        // Aadhaar details
        aadhaar<PERSON><PERSON><PERSON>,
        nameOnAadhaar,
        dateOfBirth,
        gender,
        address,
        // PAN details
        panNumber,
        panName,
        panDateOfBirth,
        // Contractor profile details
        specialties,
        portfolio,
        experience,
        serviceAreas,
    } = req.body;

    const result = await withTransaction(async (session) => {
        await User.findByIdAndUpdate(
            userId,
            { partnershipRequest: 'Contractor' },
            { session }
        );

        // Create Contractor Profile
        const contractorProfile = new Contractor({
            user: userId,
            specialties: specialties || [],
            portfolio: [], // Portfolio will be managed separately through portfolio endpoints
            experience: experience || 0,
            serviceAreas: serviceAreas || [],
        });

        await contractorProfile.save({ session });
        // Create Aadhaar record
        const aadhaar = new Aadhaar({
            userId,
            aadhaarNumber,
            nameOnAadhaar,
            dateOfBirth: new Date(dateOfBirth),
            gender,
            address,
        });
        await aadhaar.save({ session });

        // Create PAN record
        const pan = new PAN({
            userId,
            panNumber,
            panName,
            dateOfBirth: new Date(panDateOfBirth),
        });
        await pan.save({ session });

        // Handle asset storage for aadhar and pan documents
        const aadhaarAssets = [];
        const panAssets = [];

        if (
            req.files &&
            req.files.aadhaarDocument &&
            req.files.aadhaarDocument[0]
        ) {
            const aadhaarAsset = await createAssetFromFile(
                req.files.aadhaarDocument[0],
                aadhaar._id,
                'Aadhaar',
                'aadhar',
                session
            );
            if (aadhaarAsset) {
                aadhaarAssets.push(aadhaarAsset._id);
            }
        }

        if (req.files && req.files.panDocument && req.files.panDocument[0]) {
            const panAsset = await createAssetFromFile(
                req.files.panDocument[0],
                pan._id,
                'PAN',
                'pan',
                session
            );
            if (panAsset) {
                panAssets.push(panAsset._id);
            }
        }

        return { contractorProfile, aadhaarAssets, panAssets };
    });

    // Kafka producer call outside transaction
    const payload = {
        type: 'contractor',
        requesterId: userId,
    };

    await Kafka.publish(Topics.VERFICATION_REQUESTED, payload);

    res.status(201).json({
        message: 'Contractor application submitted successfully',
        applicationId: result.contractorProfile._id,
    });
};
